import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chen_kchart/chart_translations.dart';
import 'package:flutter_chen_kchart/k_chart.dart';

enum MainState { MA, BOLL, NONE }

enum SecondaryState { MACD, KDJ, RSI, WR, CCI, NONE }

class TimeFormat {
  static const List<String> YEAR_MONTH_DAY = [yyyy, '-', mm, '-', dd];
  static const List<String> YEAR_MONTH_DAY_WITH_HOUR = [yyyy, '-', mm, '-', dd, ' ', HH, ':', nn];
}

// K线图表控制器，提供程序化控制接口
class KChartController {
  _KChartWidgetState? _state;

  // 缩放状态保存
  double? _savedScale;
  double? _savedScrollX;

  void _attach(_KChartWidgetState state) {
    _state = state;
  }

  void _detach() {
    _state = null;
  }

  // 缩放到指定比例
  Future<void> scaleTo(double targetScale, {Duration? duration, Offset? center}) async {
    await _state?.scaleTo(targetScale, duration: duration, center: center);
  }

  // 放大
  Future<void> zoomIn({double factor = 1.2}) async {
    await _state?.zoomIn(factor: factor);
  }

  // 缩小
  Future<void> zoomOut({double factor = 1.2}) async {
    await _state?.zoomOut(factor: factor);
  }

  // 重置缩放
  Future<void> resetScale() async {
    await _state?.resetScale();
  }

  // 重置到默认状态（缩放和滚动位置）
  Future<void> resetToDefaultState() async {
    await _state?.resetToDefaultState();
  }

  // 保存当前缩放状态
  void saveScaleState() {
    if (_state != null) {
      _savedScale = _state!.currentScale;
      _savedScrollX = _state!.mScrollX;
    }
  }

  // 恢复保存的缩放状态
  Future<void> restoreScaleState() async {
    if (_savedScale != null && _state != null) {
      await _state!.scaleTo(_savedScale!);
      if (_savedScrollX != null) {
        _state!.mScrollX = _savedScrollX!;
        _state!.notifyChanged();
      }
    }
  }

  // 检查是否有保存的状态
  bool get hasSavedState => _savedScale != null;

  // 清除保存的状态
  void clearSavedState() {
    _savedScale = null;
    _savedScrollX = null;
  }

  // 获取当前缩放比例
  double get currentScale => _state?.currentScale ?? 1.0;

  // 是否达到最小缩放
  bool get isAtMinScale => _state?._isAtMinScale ?? false;

  // 是否达到最大缩放
  bool get isAtMaxScale => _state?._isAtMaxScale ?? false;
}

class KChartWidget extends StatefulWidget {
  final List<KLineEntity>? datas;
  final MainState mainState;
  final bool volHidden;
  final SecondaryState secondaryState;
  final Function()? onSecondaryTap;
  final bool isLine;
  final bool isTapShowInfoDialog; //是否开启单击显示详情数据
  final bool hideGrid;
  @Deprecated('Use `translations` instead.')
  final bool isChinese;
  final bool showNowPrice;
  final bool showInfoDialog;
  final bool materialInfoDialog; // Material风格的信息弹窗
  final Map<String, ChartTranslations> translations;
  final List<String> timeFormat;

  //当屏幕滚动到尽头会调用，真为拉到屏幕右侧尽头，假为拉到屏幕左侧尽头
  final Function(bool)? onLoadMore;

  final int fixedLength;
  final List<int> maDayList;
  final int flingTime;
  final double flingRatio;
  final Curve flingCurve;
  final Function(bool)? isOnDrag;
  final ChartColors? chartColors; // 改为可选参数
  final ChartStyle? chartStyle; // 改为可选参数
  final VerticalTextAlignment verticalTextAlignment;
  final bool isTrendLine;
  final double xFrontPadding;
  final bool enableTheme; // 新增：是否启用主题系统

  // 绘图工具相关
  final bool enableDrawingTools; // 是否启用绘图工具
  final DrawingToolManager? drawingToolManager; // 绘图工具管理器

  // 缩放相关配置参数
  final double minScale; // 最小缩放比例
  final double maxScale; // 最大缩放比例
  final double scaleAnimationDuration; // 缩放动画时长（毫秒）
  final Curve scaleAnimationCurve; // 缩放动画曲线
  final bool enableScaleAnimation; // 是否启用缩放动画
  final Function(double)? onScaleChanged; // 缩放变化回调
  final bool enableBoundaryFeedback; // 是否启用边界反馈
  final double scaleSensitivity; // 缩放灵敏度
  final bool enableScaleCenterPoint; // 是否启用缩放中心点控制
  final KChartController? controller; // K线图表控制器
  final bool enablePerformanceMode; // 新增：性能优化模式

  // 双指缩放和滚轮缩放配置
  final bool enablePinchZoom; // 是否启用双指缩放
  final bool enableScrollZoom; // 是否启用滚轮缩放（桌面端）
  final double scrollZoomFactor; // 滚轮缩放倍数
  final bool enableScaleHapticFeedback; // 是否启用缩放触觉反馈

  const KChartWidget(
    this.datas, {
    super.key,
    this.chartStyle,
    this.chartColors,
    this.enableTheme = true, // 默认启用主题系统
    required this.isTrendLine,
    this.xFrontPadding = 100,
    this.mainState = MainState.MA,
    this.secondaryState = SecondaryState.MACD,
    this.onSecondaryTap,
    this.volHidden = false,
    this.isLine = false,
    this.isTapShowInfoDialog = false,
    this.hideGrid = false,
    @Deprecated('Use `translations` instead.') this.isChinese = false,
    this.showNowPrice = true,
    this.showInfoDialog = true,
    this.materialInfoDialog = true,
    this.translations = kChartTranslations,
    this.timeFormat = TimeFormat.YEAR_MONTH_DAY,
    this.onLoadMore,
    this.fixedLength = 2,
    this.maDayList = const [5, 10, 20],
    this.flingTime = 350, // 更短惯性动画
    this.flingRatio = 0.9, // 更自然的惯性距离
    this.flingCurve = Curves.easeOutCubic, // 丝滑曲线
    this.isOnDrag,
    this.verticalTextAlignment = VerticalTextAlignment.left,
    // 绘图工具配置
    this.enableDrawingTools = false, // 默认关闭绘图工具
    this.drawingToolManager,
    // 缩放配置参数
    this.minScale = 0.1,
    this.maxScale = 5.0,
    this.scaleAnimationDuration = 300.0,
    this.scaleAnimationCurve = Curves.easeOutCubic,
    this.enableScaleAnimation = true,
    this.onScaleChanged,
    this.enableBoundaryFeedback = true,
    this.scaleSensitivity = 2.5, // 默认提升灵敏度
    this.enableScaleCenterPoint = true,
    this.controller,
    this.enablePerformanceMode = false, // 默认关闭性能模式
    // 双指缩放和滚轮缩放配置
    this.enablePinchZoom = true, // 默认启用双指缩放
    this.enableScrollZoom = true, // 默认启用滚轮缩放
    this.scrollZoomFactor = 1.1, // 滚轮缩放倍数
    this.enableScaleHapticFeedback = true, // 默认启用触觉反馈
  });

  @override
  _KChartWidgetState createState() => _KChartWidgetState();
}

class _KChartWidgetState extends State<KChartWidget> with TickerProviderStateMixin {
  // 优化：将全局变量移到顶部避免重复初始化
  double mScaleX = 1.0, mScrollX = 0.0, mSelectX = 0.0;
  StreamController<InfoWindowEntity?>? mInfoWindowStream;
  double mHeight = 0, mWidth = 0;
  AnimationController? _controller;
  Animation<double>? aniX;

  //For TrendLine
  List<TrendLine> lines = [];
  double? changeinXposition;
  double? changeinYposition;
  double mSelectY = 0.0;
  bool waitingForOtherPairofCords = false;
  bool enableCordRecord = false;
  // 绘图工具相关
  late DrawingToolManager _drawingToolManager;

  // 优化的缩放功能变量
  double _lastScale = 1.0;
  double _gestureStartScale = 1.0; // 手势开始时的缩放值
  bool isScale = false, isDrag = false, isLongPress = false, isOnTap = false;
  Offset? _scaleCenter; // 缩放中心点
  AnimationController? _scaleAnimationController;
  late double _currentScale;

  // 高性能缩放优化变量
  DateTime? _lastScaleUpdateTime;
  double _lastGestureScale = 1.0;
  Timer? _scaleThrottleTimer;
  bool _pendingScaleUpdate = false;
  double _pendingTargetScale = 1.0;
  static const int _scaleThrottleMs = 8; // ~120fps throttling for smooth scaling

  // 边界反馈相关
  bool _isAtMinScale = false;
  bool _isAtMaxScale = false;
  Timer? _boundaryFeedbackTimer;

  // 性能优化：节流更新
  Timer? _updateThrottleTimer;
  bool _needsUpdate = false;
  static const int _throttleDelay = 16; // 约60fps

  // 获取当前主题的颜色和样式
  ChartColors get currentChartColors {
    if (widget.enableTheme) {
      return ChartThemeManager.getColors();
    }
    return widget.chartColors ?? ChartThemeManager.getColors();
  }

  ChartStyle get currentChartStyle {
    return widget.chartStyle ?? ChartStyle();
  }

  double getMinScrollX() {
    return mScaleX;
  }

  // 获取当前绘图模式状态
  bool get _isDrawingMode {
    if (!widget.enableDrawingTools) return false;
    return _drawingToolManager.currentToolType != null;
  }

  @override
  void initState() {
    super.initState();
    mInfoWindowStream = StreamController<InfoWindowEntity?>();
    _currentScale = mScaleX;

    // 初始化绘图工具管理器
    if (widget.enableDrawingTools) {
      if (widget.drawingToolManager != null) {
        _drawingToolManager = widget.drawingToolManager!;
      } else {
        _drawingToolManager = DrawingToolManager();
      }

      _drawingToolManager.onToolsChanged = () {
        if (mounted) setState(() {});
      };
    }

    // 连接控制器
    widget.controller?._attach(this);

    // 初始化缩放动画控制器
    if (widget.enableScaleAnimation) {
      _scaleAnimationController = AnimationController(
        duration: Duration(milliseconds: widget.scaleAnimationDuration.toInt()),
        vsync: this,
      );
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    // 断开控制器连接
    widget.controller?._detach();

    mInfoWindowStream?.close();
    _controller?.dispose();
    _scaleAnimationController?.dispose();
    _boundaryFeedbackTimer?.cancel();
    _updateThrottleTimer?.cancel(); // 清理节流定时器
    _scaleThrottleTimer?.cancel(); // 清理缩放节流定时器

    super.dispose();
  }

  // 程序化缩放方法
  Future<void> scaleTo(double targetScale, {Duration? duration, Offset? center}) async {
    if (!mounted) return;

    // 输入验证
    if (targetScale.isNaN || targetScale.isInfinite) {
      return;
    }

    targetScale = targetScale.clamp(widget.minScale, widget.maxScale);

    // 如果缩放值没有变化，直接返回
    if ((targetScale - _currentScale).abs() < 0.001) {
      return;
    }

    try {
      if (widget.enableScaleAnimation && duration != null) {
        _scaleCenter = center;

        final animationController = AnimationController(
          duration: duration,
          vsync: this,
        );

        final animation = Tween<double>(
          begin: _currentScale,
          end: targetScale,
        ).animate(CurvedAnimation(
          parent: animationController,
          curve: widget.scaleAnimationCurve,
        ));

        animation.addListener(() {
          _updateScale(animation.value, center);
        });

        await animationController.forward();
        animationController.dispose();
      } else {
        _updateScale(targetScale, center);
      }
    } catch (e) {
      // Error during scale animation, set target scale directly
      _updateScale(targetScale, center);
    }
  }

  // Zoom in method
  Future<void> zoomIn({double factor = 1.2}) async {
    if (factor <= 0 || factor.isNaN || factor.isInfinite) return;
    await scaleTo(_currentScale * factor);
  }

  // Zoom out method
  Future<void> zoomOut({double factor = 1.2}) async {
    if (factor <= 0 || factor.isNaN || factor.isInfinite) return;
    await scaleTo(_currentScale / factor);
  }

  // 重置缩放
  Future<void> resetScale() async {
    await scaleTo(1.0, duration: Duration(milliseconds: widget.scaleAnimationDuration.toInt()));
  }

  // 重置到默认状态（缩放和滚动位置）
  Future<void> resetToDefaultState() async {
    // Reset scale to 1.0
    await scaleTo(1.0, duration: Duration(milliseconds: widget.scaleAnimationDuration.toInt()));

    // Reset scroll position to 0.0
    mScrollX = 0.0;
    notifyChanged();
  }

  // Update scale with performance optimization
  void _updateScale(double newScale, Offset? center) {
    // Input validation
    if (newScale.isNaN || newScale.isInfinite) return;

    final oldScale = mScaleX;
    mScaleX = newScale.clamp(widget.minScale, widget.maxScale);
    _currentScale = mScaleX;

    // 检查边界状态
    _isAtMinScale = mScaleX <= widget.minScale;
    _isAtMaxScale = mScaleX >= widget.maxScale;

    // Handle scale center point if enabled
    if (widget.enableScaleCenterPoint && center != null && oldScale > 0 && mWidth > 0 && oldScale != mScaleX) {
      final contentX = mScrollX + center.dx / oldScale;
      mScrollX = (contentX - center.dx / mScaleX).clamp(0.0, ChartPainter.maxScrollX).toDouble();
    }

    // Trigger scale change callback
    try {
      widget.onScaleChanged?.call(mScaleX);
    } catch (e) {
      // Handle callback error silently
    }

    // Boundary feedback
    if (widget.enableBoundaryFeedback) {
      _triggerBoundaryFeedback();
    }

    // 性能优化：根据性能模式选择更新策略
    if (widget.enablePerformanceMode) {
      _throttledNotifyChanged();
    } else {
      notifyChanged();
    }
  }

  // 性能优化：节流更新
  void _throttledNotifyChanged() {
    _needsUpdate = true;
    _updateThrottleTimer?.cancel();
    _updateThrottleTimer = Timer(Duration(milliseconds: _throttleDelay), () {
      if (_needsUpdate && mounted) {
        _needsUpdate = false;
        notifyChanged();
      }
    });
  }

  // 边界反馈
  void _triggerBoundaryFeedback() {
    if (_isAtMinScale || _isAtMaxScale) {
      _boundaryFeedbackTimer?.cancel();
      _boundaryFeedbackTimer = Timer(Duration(milliseconds: 100), () {
        if (mounted) {
          // 可以在这里添加触觉反馈
          if (widget.enableScaleHapticFeedback) {
            // HapticFeedback.lightImpact();
          }
        }
      });
    }
  }

  // 获取当前缩放比例
  double get currentScale => _currentScale;

  // 高性能节流缩放更新
  void _scheduleThrottledScaleUpdate(double targetScale) {
    _pendingTargetScale = targetScale;
    _pendingScaleUpdate = true;

    // 如果已有定时器在运行，不需要重新创建
    if (_scaleThrottleTimer?.isActive == true) return;

    _scaleThrottleTimer = Timer(Duration(milliseconds: _scaleThrottleMs), () {
      if (_pendingScaleUpdate && mounted) {
        _applyPendingScaleUpdate();
      }
    });
  }

  // 应用待处理的缩放更新
  void _applyPendingScaleUpdate() {
    if (!_pendingScaleUpdate) return;

    _pendingScaleUpdate = false;
    final targetScale = _pendingTargetScale;

    // 应用边界限制和橡皮筋效果
    double finalScale = targetScale;
    if (targetScale < widget.minScale) {
      // 橡皮筋效果：允许轻微超出边界
      final overshoot = widget.minScale - targetScale;
      finalScale = widget.minScale - (overshoot * 0.3);
    } else if (targetScale > widget.maxScale) {
      // 橡皮筋效果：允许轻微超出边界
      final overshoot = targetScale - widget.maxScale;
      finalScale = widget.maxScale + (overshoot * 0.3);
    }

    // 使用优化的缩放中心点计算
    _updateScaleWithOptimizedCenter(finalScale);
  }

  // 优化的缩放中心点计算
  void _updateScaleWithOptimizedCenter(double newScale) {
    if (newScale.isNaN || newScale.isInfinite) return;

    final oldScale = mScaleX;
    mScaleX = newScale;
    _currentScale = mScaleX;

    // 更新边界状态
    _isAtMinScale = mScaleX <= widget.minScale;
    _isAtMaxScale = mScaleX >= widget.maxScale;

    // 优化的缩放中心点处理
    if (widget.enableScaleCenterPoint && _scaleCenter != null && oldScale > 0 && mWidth > 0) {
      // 计算内容在缩放前的相对位置
      final relativeX = (_scaleCenter!.dx + mScrollX) / oldScale;
      // 计算新的滚动位置以保持缩放中心点
      mScrollX = (relativeX * mScaleX - _scaleCenter!.dx).clamp(0.0, ChartPainter.maxScrollX).toDouble();
    }

    // 触发回调
    try {
      widget.onScaleChanged?.call(mScaleX);
    } catch (e) {
      // Handle callback error silently
    }

    // 边界反馈
    if (widget.enableBoundaryFeedback) {
      _triggerBoundaryFeedback();
    }

    // 高效更新策略
    if (widget.enablePerformanceMode) {
      _throttledNotifyChanged();
    } else {
      notifyChanged();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.datas != null && widget.datas!.isEmpty) {
      mScrollX = mSelectX = 0.0;
      mScaleX = 1.0;
    }
    final painter = ChartPainter(
      currentChartStyle,
      currentChartColors,
      lines: lines, //For TrendLine
      xFrontPadding: widget.xFrontPadding,
      isTrendLine: widget.isTrendLine, //For TrendLine
      selectY: mSelectY, //For TrendLine
      drawingToolManager: widget.enableDrawingTools ? _drawingToolManager : null, // 新增绘图工具管理器
      datas: widget.datas,
      scaleX: mScaleX,
      scrollX: mScrollX,
      selectX: mSelectX,
      isLongPass: isLongPress,
      isOnTap: isOnTap,
      isTapShowInfoDialog: widget.isTapShowInfoDialog,
      mainState: widget.mainState,
      volHidden: widget.volHidden,
      secondaryState: widget.secondaryState,
      isLine: widget.isLine,
      hideGrid: widget.hideGrid,
      showNowPrice: widget.showNowPrice,
      sink: mInfoWindowStream?.sink,
      fixedLength: widget.fixedLength,
      maDayList: widget.maDayList,
      verticalTextAlignment: widget.verticalTextAlignment,
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        mHeight = constraints.maxHeight;
        mWidth = constraints.maxWidth;

        return Listener(
          onPointerSignal: widget.enableScrollZoom
              ? (details) {
                  if (details is PointerScrollEvent) {
                    final delta = details.scrollDelta.dy;
                    final zoomFactor = delta > 0 ? widget.scrollZoomFactor : 1.0 / widget.scrollZoomFactor;
                    final newScale = _currentScale * zoomFactor;

                    _updateScale(newScale, details.position);
                  }
                }
              : null,
          child: GestureDetector(
            onTapUp: (details) {
              if (!isLongPress && !isScale) {
                _stopAnimation();
              }

              // 处理绘图工具的点击事件
              if (widget.enableDrawingTools && _isDrawingMode) {
                final localPosition = details.localPosition;

                if (_drawingToolManager.currentToolType != null) {
                  if (_drawingToolManager.currentDrawingTool == null) {
                    _drawingToolManager.startDrawing(localPosition);
                  } else {
                    _drawingToolManager.updateDrawing(localPosition);
                    _drawingToolManager.finishDrawing();
                  }
                  return;
                } else {
                  _drawingToolManager.selectTool(localPosition);
                  return;
                }
              }

              if (!widget.isTrendLine && painter.isInMainRect(details.localPosition)) {
                isOnTap = true;
                if (mSelectX != details.localPosition.dx && widget.isTapShowInfoDialog) {
                  mSelectX = details.localPosition.dx;
                  notifyChanged();
                }
              }
              if (widget.isTrendLine && !isLongPress && enableCordRecord) {
                enableCordRecord = false;
                Offset p1 = Offset(getTrendLineX(), mSelectY);
                if (!waitingForOtherPairofCords)
                  lines.add(TrendLine(p1, Offset(-1, -1), trendLineMax!, trendLineScale!));

                if (waitingForOtherPairofCords) {
                  var a = lines.last;
                  lines.removeLast();
                  lines.add(TrendLine(a.p1, p1, trendLineMax!, trendLineScale!));
                  waitingForOtherPairofCords = false;
                } else {
                  waitingForOtherPairofCords = true;
                }
                notifyChanged();
              }
            },
            onHorizontalDragDown: (details) {
              isOnTap = false;
              _stopAnimation();
              _onDragChanged(true);
            },
            onHorizontalDragUpdate: (details) {
              if (isScale || isLongPress) return;

              // Direct pan if not in drawing mode
              if (!(widget.enableDrawingTools && _isDrawingMode && _drawingToolManager.currentDrawingTool != null)) {
                mScrollX =
                    (mScrollX + (details.primaryDelta ?? 0) / mScaleX).clamp(0.0, ChartPainter.maxScrollX).toDouble();
                notifyChanged();
                return;
              }

              // Handle drag in drawing mode
              if (widget.enableDrawingTools && _isDrawingMode && _drawingToolManager.currentDrawingTool != null) {
                _drawingToolManager.updateDrawing(details.localPosition);
                notifyChanged();
                return;
              }
            },
            onHorizontalDragEnd: (DragEndDetails details) {
              var velocity = details.velocity.pixelsPerSecond.dx;
              _onFling(velocity);
            },
            onHorizontalDragCancel: () => _onDragChanged(false),
            onScaleStart: (details) {
              if (!widget.enablePinchZoom) return;
              isScale = true;
              _gestureStartScale = mScaleX; // 记录手势开始时的真实缩放值
              _lastScale = mScaleX;
              _scaleCenter = details.focalPoint;
              _lastGestureScale = 1.0; // 重置手势缩放值
              _lastScaleUpdateTime = DateTime.now();
              _pendingScaleUpdate = false;
            },
            onScaleUpdate: (details) {
              if (!widget.enablePinchZoom) return;
              if (isLongPress && !isScale) return;

              // 高性能缩放算法：直接基于手势缩放值计算
              final gestureScale = details.scale;
              final targetScale = _gestureStartScale * gestureScale;

              // 智能节流：只在缩放值有意义变化时更新
              final scaleChange = (gestureScale - _lastGestureScale).abs();
              if (scaleChange < 0.005) return; // 过滤微小变化，减少不必要的重绘

              _lastGestureScale = gestureScale;

              // 高频率节流更新以保持流畅性
              _scheduleThrottledScaleUpdate(targetScale);
            },
            onScaleEnd: (_) {
              if (!widget.enablePinchZoom) return;
              isScale = false;

              // 确保最后一次更新被应用
              _scaleThrottleTimer?.cancel();
              if (_pendingScaleUpdate) {
                _applyPendingScaleUpdate();
              }

              // 边界回弹处理
              if (_currentScale < widget.minScale) {
                scaleTo(widget.minScale, duration: Duration(milliseconds: 200));
              } else if (_currentScale > widget.maxScale) {
                scaleTo(widget.maxScale, duration: Duration(milliseconds: 200));
              }

              _lastScale = mScaleX;
              _scaleCenter = null;
              _lastScaleUpdateTime = null;
            },
            onLongPressStart: (details) {
              isOnTap = false;
              isLongPress = true;
              if ((mSelectX != details.localPosition.dx || mSelectY != details.globalPosition.dy) &&
                  !widget.isTrendLine) {
                mSelectX = details.localPosition.dx;
                notifyChanged();
              }
              //For TrendLine
              if (widget.isTrendLine && changeinXposition == null) {
                mSelectX = changeinXposition = details.localPosition.dx;
                mSelectY = changeinYposition = details.globalPosition.dy;
                notifyChanged();
              }
              //For TrendLine
              if (widget.isTrendLine && changeinXposition != null) {
                changeinXposition = details.localPosition.dx;
                changeinYposition = details.globalPosition.dy;
                notifyChanged();
              }
            },
            onLongPressMoveUpdate: (details) {
              if ((mSelectX != details.localPosition.dx || mSelectY != details.globalPosition.dy) &&
                  !widget.isTrendLine) {
                mSelectX = details.localPosition.dx;
                mSelectY = details.globalPosition.dy;
                notifyChanged();
              }
              if (widget.isTrendLine) {
                mSelectX = mSelectX + (details.localPosition.dx - changeinXposition!);
                changeinXposition = details.localPosition.dx;
                mSelectY = mSelectY + (details.globalPosition.dy - changeinYposition!);
                changeinYposition = details.globalPosition.dy;
                notifyChanged();
              }
            },
            onLongPressEnd: (details) {
              isLongPress = false;
              enableCordRecord = true;
              mInfoWindowStream?.sink.add(null);
              notifyChanged();
            },
            child: Stack(
              children: <Widget>[
                CustomPaint(
                  size: Size(double.infinity, double.infinity),
                  painter: painter,
                ),
                if (widget.showInfoDialog) _buildInfoDialog(),
              ],
            ),
          ),
        );
      },
    );
  }

  void _stopAnimation({bool needNotify = true}) {
    if (_controller != null && _controller!.isAnimating) {
      _controller!.stop();
      _onDragChanged(false);
      if (needNotify) {
        notifyChanged();
      }
    }
  }

  void _onDragChanged(bool isOnDrag) {
    isDrag = isOnDrag;
    if (widget.isOnDrag != null) {
      widget.isOnDrag!(isDrag);
    }
  }

  void _onFling(double velocity) {
    double target = mScrollX + velocity * widget.flingRatio / mScaleX;
    target = target.clamp(0.0, ChartPainter.maxScrollX).toDouble();

    _controller = AnimationController(
      duration: Duration(milliseconds: widget.flingTime),
      vsync: this,
    );
    aniX = Tween<double>(begin: mScrollX, end: target).animate(CurvedAnimation(
      parent: _controller!,
      curve: widget.flingCurve,
    ));
    aniX!.addListener(() {
      mScrollX = aniX!.value;
      notifyChanged();
    });
    aniX!.addStatusListener((status) {
      if (status == AnimationStatus.completed || status == AnimationStatus.dismissed) {
        _onDragChanged(false);
        notifyChanged();
      }
    });
    _controller!.forward();
  }

  void notifyChanged() => setState(() {});

  Widget _buildInfoDialog() {
    return StreamBuilder<InfoWindowEntity?>(
        stream: mInfoWindowStream?.stream,
        builder: (context, snapshot) {
          // Always show popup, use default entity if no interaction
          KLineEntity entity;
          bool isInteracting = isLongPress || isOnTap;

          if (isInteracting && snapshot.hasData && snapshot.data?.kLineEntity != null) {
            // User is interacting - show specific data point
            entity = snapshot.data!.kLineEntity;
          } else if (widget.datas != null && widget.datas!.isNotEmpty) {
            // Not interacting - show latest/default data
            entity = widget.datas!.last;
          } else {
            // No data available
            return Container();
          }

          final dialogPadding = 8.0;
          final dialogWidth = mWidth * 0.4;
          final locale = Localizations.localeOf(context);

          return Positioned(
            top: widget.isLine ? 0 : 20,
            left: dialogPadding,
            child: PopupInfoView(
              entity: entity,
              width: dialogWidth,
              chartColors: currentChartColors,
              materialInfoDialog: widget.materialInfoDialog,
              fixedLength: 3,
              isLine: widget.isLine,
              locale: locale.languageCode,
              isInteracting: isInteracting,
            ),
          );
        });
  }
}
